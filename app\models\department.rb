class Department < ApplicationRecord
  # has_many :works, :dependent => :delete_all
  has_many :works, through: :positionjobs
  # has_many :responsibles, :dependent => :destroy
  has_many :positionjobs, :dependent => :destroy
  has_many :nodes, :dependent => :destroy
  has_many :ddocs, :dependent => :destroy
  has_many :node
  # has_many :positionjobs
  has_many :ddocs, class_name: "Ddoc"
  has_many :mediafiles, through: :ddocs

  # 08/05/2025
  belongs_to :organization

  belongs_to :leader_user, class_name: 'User', foreign_key: 'leader', optional: true
  belongs_to :deputy_user, class_name: 'User', foreign_key: 'deputy', optional: true
  
  def leader_name
    leader_user&.full_name || 'Chưa có trưởng nhóm'
  end
  
  def deputy_name
    deputy_user&.full_name || 'Chưa có phó nhóm'
  end
end
