require 'test_helper'

class DepartmentSubdepartmentTest < ActiveSupport::TestCase
  def setup
    # Tạo organization test
    @organization = Organization.create!(
      name: "Test Organization",
      scode: "TEST_ORG"
    )
    
    # Tạo department cha
    @parent_department = Department.create!(
      name: "Phòng IT",
      scode: "IT",
      organization: @organization
    )
    
    # Tạo user test
    @user1 = User.create!(
      first_name: "<PERSON><PERSON><PERSON>",
      last_name: "<PERSON>",
      email: "nguy<PERSON><EMAIL>",
      sid: "USER001"
    )
    
    @user2 = User.create!(
      first_name: "<PERSON><PERSON>",
      last_name: "<PERSON><PERSON> <PERSON>",
      email: "<EMAIL>", 
      sid: "USER002"
    )
  end

  test "should create valid subdepartment" do
    subdepartment = Department.new(
      name: "<PERSON><PERSON><PERSON><PERSON> triể<PERSON>",
      parents: @parent_department.id,
      amount: 5,
      leader: @user1.id,
      deputy: @user2.id,
      organization: @organization
    )
    
    assert subdepartment.valid?, "Subdepartment should be valid"
    assert subdepartment.save, "Subdepartment should save successfully"
    assert subdepartment.is_subdepartment?, "Should be identified as subdepartment"
    assert_not_nil subdepartment.scode, "Scode should be auto-generated"
  end

  test "should validate presence of name for subdepartment" do
    subdepartment = Department.new(
      parents: @parent_department.id,
      organization: @organization
    )
    
    assert_not subdepartment.valid?
    assert_includes subdepartment.errors[:name], "Tên nhóm không được để trống"
  end

  test "should validate presence of scode for subdepartment" do
    subdepartment = Department.new(
      name: "Test Group",
      parents: @parent_department.id,
      scode: "", # Explicitly set empty
      organization: @organization
    )
    
    # Scode should be auto-generated, so this should be valid
    assert subdepartment.valid?, subdepartment.errors.full_messages.join(", ")
  end

  test "should validate uniqueness of scode" do
    # Tạo subdepartment đầu tiên
    Department.create!(
      name: "Nhóm 1",
      scode: "NHOM-1",
      parents: @parent_department.id,
      organization: @organization
    )
    
    # Tạo subdepartment thứ hai với cùng scode
    subdepartment2 = Department.new(
      name: "Nhóm 2", 
      scode: "NHOM-1",
      parents: @parent_department.id,
      organization: @organization
    )
    
    assert_not subdepartment2.valid?
    assert_includes subdepartment2.errors[:scode], "Mã nhóm đã tồn tại"
  end

  test "should validate leader and deputy are different" do
    subdepartment = Department.new(
      name: "Test Group",
      parents: @parent_department.id,
      leader: @user1.id,
      deputy: @user1.id, # Same as leader
      organization: @organization
    )
    
    assert_not subdepartment.valid?
    assert_includes subdepartment.errors[:deputy], "Phó nhóm không thể trùng với trưởng nhóm"
  end

  test "should validate amount is positive" do
    subdepartment = Department.new(
      name: "Test Group",
      parents: @parent_department.id,
      amount: -1,
      organization: @organization
    )
    
    assert_not subdepartment.valid?
    assert_includes subdepartment.errors[:amount], "Số lượng phải lớn hơn 0"
  end

  test "should validate parent department exists" do
    subdepartment = Department.new(
      name: "Test Group",
      parents: 99999, # Non-existent ID
      organization: @organization
    )
    
    assert_not subdepartment.valid?
    assert_includes subdepartment.errors[:parents], "Đơn vị cha không tồn tại"
  end

  test "should not allow subdepartment of subdepartment" do
    # Tạo subdepartment
    subdepartment = Department.create!(
      name: "Nhóm con",
      parents: @parent_department.id,
      organization: @organization
    )
    
    # Thử tạo subdepartment của subdepartment
    sub_subdepartment = Department.new(
      name: "Nhóm con của nhóm con",
      parents: subdepartment.id,
      organization: @organization
    )
    
    assert_not sub_subdepartment.valid?
    assert_includes sub_subdepartment.errors[:parents], "Không thể tạo nhóm con của một nhóm con khác"
  end

  test "should validate unique name within same parent department" do
    # Tạo subdepartment đầu tiên
    Department.create!(
      name: "Nhóm Phát triển",
      parents: @parent_department.id,
      organization: @organization
    )
    
    # Thử tạo subdepartment thứ hai với cùng tên trong cùng department cha
    subdepartment2 = Department.new(
      name: "Nhóm Phát triển",
      parents: @parent_department.id,
      organization: @organization
    )
    
    assert_not subdepartment2.valid?
    assert_includes subdepartment2.errors[:name], "Tên nhóm đã tồn tại trong đơn vị này"
  end

  test "should auto generate unique scode" do
    # Tạo subdepartment với tên có dấu tiếng Việt
    subdepartment1 = Department.create!(
      name: "Nhóm Phát Triển Ứng Dụng",
      parents: @parent_department.id,
      organization: @organization
    )
    
    assert_equal "NHOM-PHAT-TRIEN-UNG-DUNG", subdepartment1.scode
    
    # Tạo subdepartment thứ hai với cùng tên
    subdepartment2 = Department.create!(
      name: "Nhóm Phát Triển Ứng Dụng",
      parents: @parent_department.id,
      organization: @organization
    )
    
    assert_equal "NHOM-PHAT-TRIEN-UNG-DUNG-1", subdepartment2.scode
  end

  test "parent department should not have subdepartment validations" do
    parent = Department.new(
      name: "", # Empty name
      scode: "", # Empty scode
      organization: @organization
      # No parents - this is a parent department
    )
    
    # Parent department should not be validated with subdepartment rules
    assert_not parent.is_subdepartment?
    # Note: Parent departments may have their own validation rules
  end
end
