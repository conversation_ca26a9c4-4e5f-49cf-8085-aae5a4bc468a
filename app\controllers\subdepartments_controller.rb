class SubdepartmentsController < ApplicationController
    include DepartmentsHelper
    before_action :authorize
    before_action :set_subdepartment, only: [:show, :edit, :update, :destroy]

    def new
        @subdepartment = Department.new
        
        respond_to do |format|
            format.html
            format.js
        end
    end
    
    def create
        department_id = params[:department_id]
        @subdepartment = Department.new(subdepartment_params)
        @subdepartment.parents = department_id
        @subdepartment.leader = params[:leader]
        @subdepartment.deputy = params[:deputy]
        
        respond_to do |format|
            if @subdepartment.save
                flash[:notice] = 'Thêm nhóm thành công!'
                format.js { render js: "window.location = '#{departments_department_details_path(lang: session[:lang], department_id: department_id, tab: TAB_NAMES[:subdepartments])}'" }
            else
                flash[:alert] = 'Thêm nhóm thất bại!'
                format.js { render :new }
            end
        end
    end
    
    def show
        respond_to do |format|
            format.html
            format.js
        end
    end
    
    def edit
        selected_subdepartment = Department.find(params[:id])
        @selected_leader = selected_subdepartment.leader.to_i
        @selected_deputy = selected_subdepartment.deputy.to_i
        respond_to do |format|
            format.html
            format.js
        end
    end
    
    def update
        respond_to do |format|
            original_department_id = params[:department_id]
            @subdepartment.leader = params[:leader]
            @subdepartment.deputy = params[:deputy]
            if @subdepartment.update(subdepartment_params)
                flash[:notice] = 'Cập nhật nhóm thành công!'
                format.js { render js: "window.location = '#{departments_department_details_path(lang: session[:lang], department_id: original_department_id, tab: TAB_NAMES[:subdepartments])}'" }
            else
                flash[:alert] = 'Cập nhật nhóm thất bại!'
                format.js { render :edit }
            end
        end
    end
    
    def destroy
        original_department_id = params[:department_id]
        @subdepartment.destroy
        flash[:notice] = 'Nhóm đã được xóa thành công!'
        render js: "window.location = '#{departments_department_details_path(lang: session[:lang], department_id: original_department_id, tab: TAB_NAMES[:subdepartments])}'"
    end

    def get_users
      search = params[:search]&.strip
      department_id = params[:department_id]

      users = User.select(:id, :last_name, :first_name, :email, :sid)
                  .select("positionjobs.name as pos_name")
                  .joins(works: [positionjob: :department])
                  .where(departments: {id: department_id})
                  .where.not(users: {status: 'INACTIVE'})
                  .order("CONCAT(users.last_name,' ', users.first_name) ASC").distinct

      if search.present?
        users = users.where("LOWER(CONCAT(users.last_name,' ', users.first_name)) LIKE ? OR users.sid LIKE ?",
          "%#{search&.downcase}%",
          "%#{search}%")
      end

      results = []
      results = users
      
      render json: { items: results }
    end

    def assign_users
        @subdepartment = Department.find(params[:id])
        respond_to do |format|
            format.html
            format.js
        end
    end

    def update_users
        # stask_ids = params[:stask_ids] || []

        # Stask.where(id: stask_ids).update_all(gtask_id: @gtask.id)

        # @gtask.stasks.where.not(id: stask_ids).update_all(gtask_id: nil)
        
        # flash[:notice] = 'Cập nhật công việc cho nhóm thành công!'
        
        # respond_to do |format|
        #     format.js { render js: "window.location = '#{works_index_path(lang: session[:lang], tab: TAB_NAMES[:gtasks])}'" }
        # end
    end

    def unassign_user
        # @stask = Stask.find(params[:stask_id])
        # @stask.update(gtask_id: nil)
        
        # flash[:notice] = "Đã gỡ bỏ công việc #{@stask.name} khỏi nhóm!"
        
        # respond_to do |format|
        # format.js { render js: "window.location = '#{works_index_path(lang: session[:lang], tab: TAB_NAMES[:gtasks])}'" }
        # end
    end

    private
    
    def set_subdepartment
        @subdepartment = Department.find(params[:id])
    rescue ActiveRecord::RecordNotFound
        respond_to do |format|
            format.html { redirect_to subdepartments_path, alert: "Không tìm thấy nhóm" }
            format.js { render js: "alert('Không tìm thấy nhóm');" }
        end
    end

    def subdepartment_params
        params.require(:department).permit(:name, :name_en, :scode, :amount, :note)
    end
end
